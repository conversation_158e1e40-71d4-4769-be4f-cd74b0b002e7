<?php

namespace Tasawk\Filament\Resources\PlanResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PricingRelationManager extends RelationManager
{
    protected static string $relationship = 'plansPricing';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('price')
                    ->required()
                    ->formatStateUsing(fn($record) => $record?->price?->formatByDecimal() ?? 0)
                    ->numeric()
                    ->suffix(__('forms.suffixes.sar')),
                Forms\Components\TextInput::make('final_price')
                    ->required()
                    ->formatStateUsing(fn($record) => $record?->final_price?->formatByDecimal() ?? 0)
                    ->numeric()
                    ->suffix(__('forms.suffixes.sar')),
                Forms\Components\TextInput::make('duration')
                    ->required()
                    ->formatStateUsing(fn($record) => $record?->duration ?? 1)
                    ->numeric()
                    ->suffix(__('forms.suffixes.month')),
                Forms\Components\Repeater::make('features')
                    ->label(__('forms.fields.features'))
                    ->relationship('features')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('forms.fields.name'))
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label(__('forms.fields.description'))
                            ->required(),
                        Forms\Components\TextInput::make('sort_order')
                            ->label(__('forms.fields.sort_order'))
                            ->numeric()
                            ->default(0),
                        Forms\Components\Toggle::make('status')
                            ->label(__('forms.fields.status'))
                            ->default(true),
                    ])
                    ->orderColumn('sort_order')
                    ->defaultItems(1)
                    ->collapsible(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(__('sections.plansPricing'))
            ->columns([
                Tables\Columns\TextColumn::make('index')->rowIndex(),
                Tables\Columns\TextColumn::make('id'),
                TextColumn::make('plan.name')->label(__('forms.fields.name'))->toggleable(false),
                TextColumn::make('price')->toggleable(false),
                TextColumn::make('final_price')->toggleable(false),
                TextColumn::make('duration')
                ->suffix(__('forms.suffixes.month'))
                ->toggleable(false),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }
    public static function getModelLabel(): ?string
    {
        return __('sections.plansPricing');
    }
}
