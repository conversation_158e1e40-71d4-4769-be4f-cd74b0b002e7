<!-- Start Packages Section (Arabic) -->
<section id="packages-sec" class="page-packages-sec page-general-sec">
    <div class="container">
      <div class="packages-tabs">
        <?php
          // Collect all unique durations from all plans' pricing
          $allDurations = collect();
          foreach($plans as $plan) {
            $allDurations = $allDurations->merge($plan->plansPricing->pluck('duration'));
          }
          $allDurations = $allDurations->unique()->sort()->values();
        ?>
        <div class="tabs-responsive">
          <div class="nav custom-nav-tabs">
            <?php $__currentLoopData = $allDurations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $duration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <button class="<?php echo e($i === 0 ? 'active' : ''); ?>" data-bs-toggle="tab" data-bs-target="#duration-<?php echo e($duration); ?>">
                <?php echo e($duration); ?> <?php echo e($duration <= 2 ? 'شهر' : 'شهور'); ?>

              </button>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </div>
        </div>
        <div class="tab-content">
          <?php $__currentLoopData = $allDurations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $duration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="tab-pane fade <?php echo e($i === 0 ? 'show active' : ''); ?>" id="duration-<?php echo e($duration); ?>">
              <div class="packages-table-content">
                <table class="packages-table">
                  <thead>
                    <tr>
                      <th></th>
                      <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php $pricing = $plan->plansPricing->where('duration', $duration)->first(); ?>
                        <?php if($pricing): ?>
                          <th>
                            <div class="package-head">
                              <i class="far fa-dumbbell"></i>
                              <h3 class="package-title"><?php echo e($plan->getTranslation('name', 'ar')); ?></h3>
                            </div>
                          </th>
                        <?php endif; ?>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td></td>
                      <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php $pricing = $plan->plansPricing->where('duration', $duration)->first(); ?>
                        <?php if($pricing): ?>
                          <td>
                            <div class="tbody-head">
                              <strong class="package-price">
                                <?php if($pricing->isDiscount()): ?>
                                  <span class="new-price"><?php echo e($pricing->final_price->formatByDecimal()); ?> ريال</span>
                                <?php else: ?>
                                  <span class="price"><?php echo e($pricing->final_price->formatByDecimal()); ?> ريال</span>
                                <?php endif; ?>
                              </strong>
                              <span class="package-duration">(<?php echo e($pricing->duration); ?> <?php echo e($pricing->duration <= 2 ? 'شهر' : 'شهور'); ?>)</span>
                              <a href="#" class="package-btn">ابدأ الأن</a>
                            </div>
                          </td>
                        <?php endif; ?>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                    <?php
                      // Get the maximum number of notes among all plans for the current language
                      $maxNotes = 0;
                      foreach($plans as $plan) {
                        $notes = $plan->getTranslation('notes', 'ar');
                        if (is_array($notes)) {
                          $maxNotes = max($maxNotes, count($notes));
                        }
                      }
                    ?>
                    <?php for($ni = 0; $ni < $maxNotes; $ni++): ?>
                      <tr>
                        <td>
                          <?php
                            $note = '';
                            foreach($plans as $plan) {
                              $notes = $plan->plansPricing->where('duration', $duration)->first()
                                ? $plan->getTranslation('notes', 'ar') : null;
                              if (is_array($notes) && isset($notes[$ni])) {
                                $note = $notes[$ni];
                                if (is_array($note)) $note = implode(', ', $note);
                                break;
                              }
                            }
                          ?>
                          <h3 class="package-info-head"><?php echo e($plan->getTranslation('name', 'ar')); ?></h3>
                        </td>
                        <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          <?php $pricing = $plan->plansPricing->where('duration', $duration)->first(); ?>
                          <?php if($pricing): ?>
                            <td>
                              <ul class="package-info-body">
                                <li>
                                  <?php
                                    $desc = $plan->getTranslation('description', 'ar');
                                    if (is_array($desc)) {
                                      $desc = $desc[$ni] ?? '';
                                      if (is_array($desc)) $desc = implode(', ', $desc);
                                    }
                                  ?>
                                  <?php echo e($desc); ?>

                                </li>
                              </ul>
                            </td>
                          <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </tr>
                    <?php endfor; ?>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td></td>
                      <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php $pricing = $plan->plansPricing->where('duration', $duration)->first(); ?>
                        <?php if($pricing): ?>
                          <td>
                            <a href="#" class="package-btn">ابدأ الأن</a>
                          </td>
                        <?php endif; ?>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
      </div>
    </div>
  </section>

  <section class="download-app">
    <div class="download-bg">
        <div class="container">
            <div class="download-parent">
                <img class="cta-mob wow animate__fadeInRight animate__animated"
                    src="<?php echo e(asset('assets/images/cta-mobile.png')); ?>" alt="">
                <div class="content wow animate__fadeInDown animate__animated" >
                    <h3 class="head">
                        <?php echo e(trans('site.download_app_now')); ?>

                    </h3>
                    <p class="para">
                        <?php echo e(trans('site.app_desc')); ?>

                    </p>
                    <div class="download-links">
                        <a href="<?php echo e($google_play_link); ?>" target="_blank">
                            <img class="img-fluid" src="<?php echo e(asset('assets/images/google-play.svg')); ?>" alt="">
                        </a>
                        <a href="<?php echo e($apple_store_link); ?>" target="_blank">
                            <img class="img-fluid" src="<?php echo e(asset('assets/images/app-store.svg')); ?>" alt="">
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>

</section>

<!-- End download app -->


<!-- Start contact -->
<?php echo $__env->make('site.includes.contact', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<!-- End contact -->
  <!-- End Packages Section (Arabic) -->
<?php /**PATH D:\Workstation\Taswk\nitro\resources\views/site/includes/packages.blade.php ENDPATH**/ ?>