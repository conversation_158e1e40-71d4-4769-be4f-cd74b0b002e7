<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Tasawk\Models\Plan;

class PlanTitleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample title data for plans
        $planTitles = [
            [
                'name_ar' => 'الباقة الأساسية',
                'name_en' => 'Basic Plan',
                'title' => [
                    'ar' => 'باقة البداية',
                    'en' => 'Starter Package'
                ]
            ],
            [
                'name_ar' => 'الباقة المتقدمة',
                'name_en' => 'Advanced Plan',
                'title' => [
                    'ar' => 'باقة المحترفين',
                    'en' => 'Professional Package'
                ]
            ],
            [
                'name_ar' => 'الباقة المميزة',
                'name_en' => 'Premium Plan',
                'title' => [
                    'ar' => 'باقة النخبة',
                    'en' => 'Elite Package'
                ]
            ],
            [
                'name_ar' => 'باقة اللياقة',
                'name_en' => 'Fitness Plan',
                'title' => [
                    'ar' => 'باقة اللياقة الشاملة',
                    'en' => 'Complete Fitness Package'
                ]
            ],
            [
                'name_ar' => 'باقة التغذية',
                'name_en' => 'Nutrition Plan',
                'title' => [
                    'ar' => 'باقة التغذية الصحية',
                    'en' => 'Healthy Nutrition Package'
                ]
            ],
            [
                'name_ar' => 'الباقة الشاملة',
                'name_en' => 'Complete Plan',
                'title' => [
                    'ar' => 'الباقة الذهبية',
                    'en' => 'Golden Package'
                ]
            ]
        ];

        $this->updateExistingPlans($planTitles);
    }

    /**
     * Update existing plans with title data
     */
    private function updateExistingPlans($planTitles)
    {
        $plans = Plan::all();

        $this->command->info("Found {$plans->count()} existing plans to update.");

        foreach ($plans as $index => $plan) {
            // Get title data for this plan (cycle through available titles)
            $titleData = $planTitles[$index % count($planTitles)];

            // Update the plan with title
            $plan->update([
                'title' => $titleData['title']
            ]);

            $this->command->info("Updated plan ID {$plan->id} with title: {$titleData['title']['ar']} / {$titleData['title']['en']}");
        }

        $this->command->info("Successfully updated {$plans->count()} plans with title data.");
    }
}
