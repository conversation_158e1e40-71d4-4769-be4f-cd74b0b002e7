<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Tasawk\Models\Plan;
use Tasawk\Models\PlansPricing;

class CompletePlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Complete plan data with titles, descriptions, and notes
        $plansData = [
            [
                'title' => [
                    'ar' => 'باقة البداية الصحية',
                    'en' => 'Healthy Start Package'
                ],
                'name' => [
                    'ar' => 'الباقة الأساسية',
                    'en' => 'Basic Plan'
                ],
                'description' => [
                    'ar' => [
                        'برنامج تغذية متوازن',
                        'متابعة أسبوعية',
                        'استشارة غذائية',
                        'خطة تمارين أساسية'
                    ],
                    'en' => [
                        'Balanced nutrition program',
                        'Weekly follow-up',
                        'Nutritional consultation',
                        'Basic exercise plan'
                    ]
                ],
                'notes' => [
                    'ar' => [
                        'عدد الوجبات',
                        'المتابعة',
                        'التمارين',
                        'الاستشارات'
                    ],
                    'en' => [
                        'Number of meals',
                        'Follow-up',
                        'Exercises',
                        'Consultations'
                    ]
                ],
                'pricing' => [
                    ['duration' => 1, 'price' => 299, 'final_price' => 299],
                    ['duration' => 3, 'price' => 799, 'final_price' => 699],
                    ['duration' => 6, 'price' => 1499, 'final_price' => 1299]
                ]
            ],
            [
                'title' => [
                    'ar' => 'باقة المحترفين المتقدمة',
                    'en' => 'Advanced Professional Package'
                ],
                'name' => [
                    'ar' => 'الباقة المتقدمة',
                    'en' => 'Advanced Plan'
                ],
                'description' => [
                    'ar' => [
                        'برنامج تغذية شامل',
                        'متابعة يومية',
                        'استشارات متعددة',
                        'خطة تمارين متقدمة',
                        'تحليل جسم شامل'
                    ],
                    'en' => [
                        'Comprehensive nutrition program',
                        'Daily follow-up',
                        'Multiple consultations',
                        'Advanced exercise plan',
                        'Complete body analysis'
                    ]
                ],
                'notes' => [
                    'ar' => [
                        'عدد الوجبات',
                        'المتابعة',
                        'التمارين',
                        'الاستشارات',
                        'التحاليل'
                    ],
                    'en' => [
                        'Number of meals',
                        'Follow-up',
                        'Exercises',
                        'Consultations',
                        'Analysis'
                    ]
                ],
                'pricing' => [
                    ['duration' => 1, 'price' => 499, 'final_price' => 449],
                    ['duration' => 3, 'price' => 1299, 'final_price' => 1099],
                    ['duration' => 6, 'price' => 2399, 'final_price' => 1999]
                ]
            ]
        ];

        $this->createPlans($plansData);
    }

    /**
     * Create plans with complete data
     */
    private function createPlans($plansData)
    {
        foreach ($plansData as $planData) {
            $pricing = $planData['pricing'];
            unset($planData['pricing']);

            // Create the plan
            $plan = Plan::create($planData);

            // Create pricing for the plan
            foreach ($pricing as $priceData) {
                PlansPricing::create([
                    'plan_id' => $plan->id,
                    'duration' => $priceData['duration'],
                    'price' => $priceData['price'] * 100, // Convert to cents
                    'final_price' => $priceData['final_price'] * 100, // Convert to cents
                    'status' => true
                ]);
            }

            $this->command->info("Created plan: {$planData['title']['ar']} / {$planData['title']['en']}");
        }

        $this->command->info("Successfully created " . count($plansData) . " complete plans with pricing.");
    }
}
