<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Traits\Publishable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Cknow\Money\Casts\MoneyDecimalCast;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Casts\Attribute;

class PlansPricing extends Model
{
    use HasFactory, Publishable, SoftDeletes;


    protected $fillable = [
        'plan_id',
        'price',
        'final_price',
        'duration',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
        // 'price' => MoneyDecimalCast::class,
        // 'final_price' => MoneyDecimalCast::class,

    ];
    public function price(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Money::parse($value)
        );
    }

    public function finalPrice(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Money::parse($value)
        );
    }

    public function plan() {
        return $this->belongsTo(Plan::class,'plan_id');
    }

    public function features()
    {
        return $this->hasMany(PlanFeature::class, 'plan_price_id')->orderBy('sort_order');
    }

    // check if plan is discount
    public function isDiscount() {
        if($this->final_price->formatByDecimal() < $this->price->formatByDecimal()  && $this->final_price->formatByDecimal() > 0) {
            return true;
        }
        return false;
    }
}
