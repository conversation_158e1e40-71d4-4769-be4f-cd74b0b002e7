<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plan_features', function (Blueprint $table) {
            // Add plan_price_id foreign key
            $table->foreignId('plan_price_id')->nullable()->after('plan_id')->constrained('plans_pricings')->onDelete('cascade');

            // Make plan_id nullable since we'll use plan_price_id instead
            $table->foreignId('plan_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plan_features', function (Blueprint $table) {
            $table->dropForeign(['plan_price_id']);
            $table->dropColumn('plan_price_id');
            $table->foreignId('plan_id')->nullable(false)->change();
        });
    }
};
