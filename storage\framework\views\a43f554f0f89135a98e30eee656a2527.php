<?php $__env->startSection('title', __('Packages')); ?>
<?php $__env->startSection('header-page', 'pages-body'); ?>

<?php $__env->startSection('logo'); ?>
    <div class="logo">
        <a href="<?php echo e(route('home')); ?>">
            <img src="<?php echo e($logo_light); ?>" alt="logo">
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <style>
        header {
            background-color: #fff !important;
        }
        .header-content {
            justify-content: center !important;
        }

        /* Desktop navigation styles */
        @media only screen and (min-width: 992px) {
            .navigation {
                display: flex !important;
                justify-content: center !important;
                width: 100%;
            }
            .big-menu {
                justify-content: center !important;
                width: 100%;
            }
        }

        /* Mobile navigation - keep default behavior */
        @media only screen and (max-width: 991px) {
            .navigation {
                display: none; /* Hidden by default on mobile */
            }
            .header-content {
                justify-content: space-between !important;
            }
            .links-bars-holder {
                display: flex !important;
            }
            .openBtn {
                display: flex !important;
                position: relative !important;
                z-index: 1002 !important;
                background: none !important;
                border: none !important;
                cursor: pointer !important;
            }
            .openBtn i {
                color: #004080 !important;
                font-size: 24px !important;
            }
        }

        .big-menu li a {
            color: #004080 !important;
        }
        .openBtn i {
            color: #004080 !important;
        }
        .header-links a {
            background-color: #fff !important;
            border: 2px solid #004080 !important;
        }
        .header-links a i {
            color: #004080 !important;
        }
        .page-packages-sec, .packages-tabs {
            margin-top: 90px !important;
        }
    </style>

    <?php echo $__env->make('site.includes.packages-en', ['plans' => $plans], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Workstation\Taswk\nitro\resources\views/site/packages-en-page.blade.php ENDPATH**/ ?>