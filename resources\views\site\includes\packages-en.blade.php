<!-- Start Packages Section (English) -->
<section id="packages-sec" class="page-packages-sec page-general-sec">
  <div class="container">
    <div class="packages-tabs">
      @php
        // Collect all unique durations from all plans' pricing
        $allDurations = collect();
        foreach($plans as $plan) {
          $allDurations = $allDurations->merge($plan->plansPricing->pluck('duration'));
        }
        $allDurations = $allDurations->unique()->sort()->values();
      @endphp
      <div class="tabs-responsive">
        <div class="nav custom-nav-tabs">
          @foreach($allDurations as $i => $duration)
            <button class="{{ $i === 0 ? 'active' : '' }}" data-bs-toggle="tab" data-bs-target="#duration-{{ $duration }}">
              {{ $duration }} {{ $duration == 1 ? 'Month' : 'Months' }}
            </button>
          @endforeach
        </div>
      </div>
      <div class="tab-content">
        @foreach($allDurations as $i => $duration)
          <div class="tab-pane fade {{ $i === 0 ? 'show active' : '' }}" id="duration-{{ $duration }}">
            <div class="packages-table-content">
              <table class="packages-table">
                <thead>
                  <tr>
                    <th></th>
                    @foreach($plans as $plan)
                      @php $pricing = $plan->plansPricing->where('duration', $duration)->first(); @endphp
                      @if($pricing)
                        <th>
                          <div class="package-head">
                            <i class="far fa-dumbbell"></i>
                            <h3 class="package-title">{{  $plan->getTranslation('name', 'en') }}</h3>
                          </div>
                        </th>
                      @endif
                    @endforeach
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td></td>
                    @foreach($plans as $plan)
                      @php $pricing = $plan->plansPricing->where('duration', $duration)->first(); @endphp
                      @if($pricing)
                        <td>
                          <div class="tbody-head">
                            <strong class="package-price">
                              @if($pricing->isDiscount())
                                <span class="old-price">{{ $pricing->price->formatByDecimal() }} SAR</span>
                                <span class="new-price">{{ $pricing->final_price->formatByDecimal() }} SAR</span>
                              @else
                                <span class="price">{{ $pricing->price->formatByDecimal() }} SAR</span>
                              @endif
                            </strong>
                            <span class="package-duration">({{ $pricing->duration }} {{ $pricing->duration == 1 ? 'Month' : 'Months' }})</span>
                            <a href="#" class="package-btn">Start Now</a>
                          </div>
                        </td>
                      @endif
                    @endforeach
                  </tr>
                  @php
                    // Get the maximum number of notes among all plans for the current language
                    $maxNotes = 0;
                    foreach($plans as $plan) {
                      $notes = $plan->getTranslation('notes', 'en');
                      if (is_array($notes)) {
                        $maxNotes = max($maxNotes, count($notes));
                      }
                    }
                  @endphp
                  @php
                    $maxFeatures = $plans->map(function($plan) use ($duration) {
                      $pricing = $plan->plansPricing->where('duration', $duration)->first();
                      return $pricing ? $pricing->features->count() : 0;
                    })->max();
                  @endphp
                  @for($fi = 0; $fi < $maxFeatures; $fi++)
                    <tr>
                      <td>
                        @php
                          $featureName = '';
                          foreach($plans as $plan) {
                            $pricing = $plan->plansPricing->where('duration', $duration)->first();
                            if ($pricing) {
                              $feature = $pricing->features->skip($fi)->first();
                              if ($feature) {
                                $featureName = $feature->getTranslation('name', 'en');
                                break;
                              }
                            }
                          }
                        @endphp
                        <h3 class="package-info-head">{{ $featureName ?: '-' }}</h3>
                      </td>
                      @foreach($plans as $plan)
                        @php $pricing = $plan->plansPricing->where('duration', $duration)->first(); @endphp
                        @if($pricing)
                          <td>
                            <ul class="package-info-body">
                              <li>
                                @php
                                  $feature = $pricing->features->skip($fi)->first();
                                  $desc = $feature ? $feature->getTranslation('description', 'en') : '';
                                @endphp
                                {{ $desc ?: '-' }}
                              </li>
                            </ul>
                          </td>
                        @endif
                      @endforeach
                    </tr>
                  @endfor
                </tbody>
                <tfoot>
                  <tr>
                    <td></td>
                    @foreach($plans as $plan)
                      @php $pricing = $plan->plansPricing->where('duration', $duration)->first(); @endphp
                      @if($pricing)
                        <td>
                          <a href="#" class="package-btn">Start Now</a>
                        </td>
                      @endif
                    @endforeach
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        @endforeach
      </div>
    </div>
  </div>
</section>
<!-- End Packages Section (English) -->
<section class="download-app">
    <div class="download-bg">
        <div class="container">
            <div class="download-parent">
                <img class="cta-mob wow animate__fadeInRight animate__animated"
                    src="{{ asset('assets/images/cta-mobile.png') }}" alt="">
                <div class="content wow animate__fadeInDown animate__animated" >
                    <h3 class="head">
                        {{ trans('site.download_app_now') }}
                    </h3>
                    <p class="para">
                        {{ trans('site.app_desc') }}
                    </p>
                    <div class="download-links">
                        <a href="{{ $google_play_link }}" target="_blank">
                            <img class="img-fluid" src="{{ asset('assets/images/google-play.svg') }}" alt="">
                        </a>
                        <a href="{{ $apple_store_link }}" target="_blank">
                            <img class="img-fluid" src="{{ asset('assets/images/app-store.svg') }}" alt="">
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>

</section>

<!-- End download app -->


<!-- Start contact -->
@include('site.includes.contact')
<!-- End contact -->