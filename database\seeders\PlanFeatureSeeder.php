<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Tasawk\Models\Plan;
use Tasawk\Models\PlanFeature;

class PlanFeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample features for plans
        $featuresData = [
            [
                'name' => [
                    'ar' => 'عدد الوجبات اليومية',
                    'en' => 'Daily Meals Count'
                ],
                'description' => [
                    'ar' => 'تحديد احتياجات اليوم من السعرات حسب وزنك وطولك ومعدلك',
                    'en' => 'Determine daily calorie needs based on your weight, height and rate'
                ]
            ],
            [
                'name' => [
                    'ar' => 'المتابعة',
                    'en' => 'Follow-up'
                ],
                'description' => [
                    'ar' => 'توزيع الماكروز (بروتين، كربوهيدرات، دهون) بطريقة متوازنة',
                    'en' => 'Distribute macros (protein, carbs, fats) in a balanced way'
                ]
            ],
            [
                'name' => [
                    'ar' => 'التمارين',
                    'en' => 'Exercises'
                ],
                'description' => [
                    'ar' => 'جدول وجبات يومية بخيارات متنوعة',
                    'en' => 'Daily meal schedule with various options'
                ]
            ],
            [
                'name' => [
                    'ar' => 'الاستشارات',
                    'en' => 'Consultations'
                ],
                'description' => [
                    'ar' => 'وسيلة التطبيق',
                    'en' => 'Application method'
                ]
            ],
            [
                'name' => [
                    'ar' => 'دلائل غذائية',
                    'en' => 'Nutritional Guidelines'
                ],
                'description' => [
                    'ar' => 'دلائل غذائية ونصائح علمية لرفعت السعرات الأكل خارج البيت',
                    'en' => 'Nutritional guidelines and scientific tips for eating out'
                ]
            ]
        ];

        $plans = Plan::all();

        foreach ($plans as $plan) {
            foreach ($featuresData as $index => $featureData) {
                PlanFeature::create([
                    'plan_id' => $plan->id,
                    'name' => $featureData['name'],
                    'description' => $featureData['description'],
                    'sort_order' => $index + 1,
                    'status' => true
                ]);
            }

            $this->command->info("Created features for plan: {$plan->getTranslation('name', 'ar')}");
        }

        $this->command->info("Successfully created features for all plans.");
    }
}
