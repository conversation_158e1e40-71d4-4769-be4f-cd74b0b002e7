<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Tasawk\Traits\Publishable;

class PlanFeature extends Model
{
    use HasFactory, HasTranslations, Publishable;

    public array $translatable = ['name', 'description'];

    protected $fillable = [
        'plan_id',
        'name',
        'description',
        'sort_order',
        'status',
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
        'status' => 'boolean',
    ];

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }
}
