<?php

return [
    'inbody_keys' => [
        'chest' => 'chest',
        // 'waist' => 'middle',
        'arm' => 'arm',
        // 'thigh' => 'thigh',
        // 'calf' => 'leg',
        'chest_circumference' => 'Chest circumference',
        'lower_chest' => 'Lower chest',
        'waist' => 'Waist',
        'abdomen' => 'Abdomen',
        'buttocks' => 'Buttocks',
        'upper_arm' => 'Humerus',
        'thigh' => 'Thigh',
        'calf' => 'Calf',
        'left_arm' => 'Left arm',
        'right_arm' => 'Right arm',
        'stomach' => 'Stomach',
        'right_thigh' => 'Right thigh',
        'left_thigh' => 'Left thigh',
        'right_leg' => 'Right leg',
        'left_leg' => 'Left leg',
    ],
    'fields' => [
        'show_subscriptions' => 'Show subscriptions',
        'id' => 'ID',
        'order_type' => 'Order type',
        'title' => 'Title',
        'text' => 'Text',
        'address_name' => 'Address name',
        'image' => 'Image',
        'avatar' => 'Avatar',
        'name' => 'Name',
        'app_address' => 'App address',
        'type' => 'Type',
        'category_parent_id' => 'Parent Category',
        'email' => 'Email',
        'takeaway_discount' => 'Takeaway discount',
        'phone' => 'Phone',
        'active' => 'Active',
        'customer_name' => 'Customer',
        'zone_name' => 'Zone',
        'location' => 'Location',
        'map_location' => 'Map location',
        'state' => 'State',
        'managers' => 'Managers',
        'receipt_method' => 'Receipt method',
        'male' => 'Male',
        'female' => 'Female',
        'contact_type' => 'Contact message type',
        'city_name' => 'city name',
        'maintenance_mode' => 'Maintenance mode',
        'heavy_load_mode' => 'Heavy load mode',
        'street' => 'Street',
        'building_number' => 'Building number',
        'floor' => 'Floor',
        'primary' => 'Primary',
        'password' => 'Password',
        'password_confirmation' => 'Password confirmation',
        'notes' => 'Notes',
        'product_title' => 'Item title',
        'product_name' => 'Item name',
        'from' => 'From',
        'to' => 'To',
        'status' => 'Status',
        'options' => 'Options',
        'required' => 'Required',
        'values' => 'Values',
        'value_name' => 'Value name',
        'price' => 'Price',
        'option_name' => 'Option name',
        'weekdays' => [
            'saturday' => 'Saturday',
            'sunday' => 'Sunday',
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
        ],
        'manager_name' => 'Manager name',
        'phone_verified' => 'Phone verified',
        'enabled' => 'Enabled',
        'available' => 'Available',
        'brand_id' => 'Brand',
        'brand_name' => 'Brand',
        'publish_date' => 'Published date',
        'categories' => 'Categories',
        'national_id' => 'National id',
        'qualification' => 'Qualification',
        'cv' => 'CV',
        'company_id' => 'Company',
        'company_name' => 'Company',
        'products_count' => 'Items count',
        'description' => 'Description',
        'type_name' => 'Type name',
        'message' => 'Message',
        'seen' => 'Seen',
        'app_logo' => 'App logo',
        'app_name' => 'App name',
        'app_email' => 'App email',
        'app_phone' => 'App phone',
        'enable_delivery_mode' => 'Enable delivery mode',
        'enable_orders_discount_upon_receipt_from_the_branch' => 'Enable orders discount upon receipt from the branch',
        'delivery_cost' => 'Delivery cost',
        'orders_discount_percentage_upon_receipt_from_the_branch' => 'Orders discount percentage upon receipt from the branch',
        'diameter' => 'Diameter',
        'delivery_cost_for_each_additional_kilometer' => 'Delivery cost for each additional kilometer',
        'order_limit_per_item' => 'Orders limit per item',
        'google_play_link' => 'Google play link',
        'apple_store_link' => 'Apple store link',
        'about_us' => 'About us',
        'terms_and_conditions' => 'Terms and conditions',
        'privacy_policy' => 'Privacy policy',
        'icon' => 'Icon',
        'link' => 'Link',
        'notifiable' => 'Notifiable',
        'notification_title' => 'Notification title',
        'notification_body' => 'Notification body',
        'recommendations' => 'Recommendations',
        'payment_status' => 'Payment status',
        'total' => 'Total',
        'quantity' => 'Quantity',
        'subTotal' => 'SubTotal',
        'taxes' => 'Taxes',
        'delivery' => 'Delivery',
        'reason_id' => 'Reason',
        'comment' => 'Comment',
        'branch_name' => 'Branch name',
        'date' => 'Date',

        'food_quality' => 'Food quality',
        'service_quality' => 'Service quality',
        'delivery_speed' => 'Delivery speed',
        'serving_food_quality' => 'Serving food quality',
        'today_orders' => 'Today orders',
        'min_time_to_deliver_order' => 'Min time to deliver order',
        'max_time_to_deliver_order' => 'Max time to deliver order',
        'role' => 'Role',
        'roles_name' => 'Role',
        'receipt_methods' => 'Receipt methods',
        'message_title' => 'Message title',
        'message_body' => 'Message body',
        'firebase_server_key' => 'Firebase server key',
        'firebase_server_id' => 'Firebase server id',
        'google_map_key' => 'Google map key',
        'enable_maintenance_mode' => 'Enable maintenance mode',
        'enable_heavy_load_mode' => 'Enable heavy load mode',
        'mark_as_seen' => 'Mark as seen',
        'orders_count' => 'Orders count',
        'index' => 'Index',
        'order_number' => 'Order number',
        'message_on_order_created' => 'Message on order created',
        'message_on_order_on_the_way' => 'Message on order on the way',
        'message_on_order_is_delivered' => 'Message on order is delivered',
        'message_on_receipt_from_branch' => 'Message on receipt from branch',
        'data_place_name' => 'Place name',
        'data_length' => 'Length',
        'data_width' => 'Width',
        'data_notes' => 'Notes',
        'data_location' => 'Location',
        'papers_required_to_be_completed' => 'Papers required to be completed',
        'customers_count' => 'Number of registered customers',
        'image_en' => 'Image in english',
        'image_ar' => 'Image in arabic',
        'title_ms' => 'Title',
        'gender' => 'Gender',
        'city_id' => 'City',
        'app_whatsapp' => 'WhatsApp number',
        'zone_id' => 'Zone',
        'coupon_code' => 'Coupon code',
        'discount_type' => 'Discount type',
        'discount_value' => 'Discount value',
        'start_date' => 'Start date',
        'end_date' => 'End date',
        'usages' => 'Usages',
        'usage_per_user' => 'Usage per user',
        'used_times' => 'Used times',
        'used_at' => 'Used at',
        'usage_status' => 'Usage status',
        'directed_to' => 'Directed to',
        'seen_status' => 'Message status',
        'not_seen' => 'Not seen',
        'job_title' => 'Job',
        'certificat' => 'Certificate',
        'sub_images' => 'Sub images',
        'note' => 'Note',
        'final_price' => 'Price after discount',
        'duration' => 'Duration',
        'day' => 'Day',
        'month' => 'Month',
        'year' => 'Year',
        'image_before' => 'Image before',
        'image_after' => 'Image after',
        'size_id' => 'Size/weight',
        'size_name' => 'Size/weight',
        'value' => 'Value',
        'number_of_session' => 'Sessions',
        'number_of_repetition' => 'Number of repetitions',
        'rest' => 'Rest between sessions',
        'sort' => 'Sort',
        'video' => 'Video',
        'option_id' => 'Option',
        'option_value_id' => 'Option value',
        'option_value_name' => 'Option value',
        'sum_of_protien' => 'Sum of protein',
        'sum_of_carbohydrate' => 'Sum of carbohydrate',
        'sum_of_fat' => 'Sum of fat',
        'sum_of_vitamin' => 'Sum of vitamin',
        'sum_of_calories' => 'Sum of calories',
        'sum_of_carbohydrate' => 'Sum of carbohydrate',
        'unit_id' => 'Size/weight',
        'unit_name' => 'Size/weight',
        'meal_id' => 'Meal',
        'meal_name' => 'Meal',
        'category_id' => 'Category',
        'category_name' => 'Category',
        'exercise_id' => 'Exercise',
        'session' => 'Session',
        'minute' => 'Minute',
        'time' => 'Time',
        'create_date' => 'Create date',
        'subscription_number' => 'Subscription number',
        'change_status' => 'Order status changed',
        'assign-worker' => 'Change worker',
        'incoice_url' => 'Invoice link',
        'invoice_no' => 'Invoice number',
        'paid_at' => 'Payment date',
        'payment_method' => 'Payment method',
        'value_paid' => 'Amount paid',
        'invoice_url' => 'Show invoice',
        'not_seen' => 'Un read',
        'seen_status' => 'Message status',
        'create_order_date' => 'Order creation date and time',
        'order_time' => 'Service execution time',
        'order_date' => 'Service execution date',
        'sar' => 'SAR',
        'download_invoice' => 'Download invoice',
        'category_name' => 'Category',
        'date_status' => 'Date of status change',
        'subTotal' => 'Total without tax',
        'show_invoice' => 'Show invoice',
        'cancellation_reason_name' => 'Reason',
        'created_at' => 'Created at',
        'rate' => 'Rate',
        'question' => 'Question',
        'answer' => 'Answer',
        'question_type' => 'Question type',
        'assign-program' => 'Assign program',
        'program' => 'Program',
        'plan_name' => 'Plan',
        'subscription_id' => 'Subscription id',
        'program_id' => 'Program id',
        'program_name' => 'Program name',
        'program_duration' => 'Program duration',
        'invoice_url' => 'Invoice link',
        'coupon_discount' => 'Coupon discount',
        'send_type' => 'Send type',
        'registered_customers' => 'Active registrants',
        'customers_subscription_expired' => 'Customers subscription expired',
        'write_message' => 'Write message',
        'total_amount' => 'Paid amount',
        'invoice_id' => 'Invoice id',
        'retun_at' => 'Return date',
        'default_program' => 'Current programme',
        'chest_circumference' => 'Chest circumference',
        'lower_chest' => 'Lower chest',
        'waist' => 'Waist',
        'abdomen' => 'Abdomen',
        'buttocks' => 'Buttocks',
        'upper_arm' => 'Humerus',
        'thigh' => 'Thigh',
        'calf' => 'Calf',
        'inbody_type' => 'Body scan type',
        'products' => 'Products',
        'nutritional_supplement_id' => 'Name of nutritional supplement',
        'amount' => 'Amount',
        'is_verify' => 'Phone verified',
        'sum_of_sugars' => 'Sum of sugars',
        'save_as_draft' => 'Save as draft',
        'not_available' => 'Not yet determined',
        'all' => 'All',
        'subscribers' =>'Subscribers',
        'non_subscribers' => 'Non-subscribers',
        'invoice_text' => 'Valuable private consultation',
        'main_image' => 'Main image',
        'mockup' => 'Sub image',
        'is_renew' => 'Renewal date',
        'is_dependency' => 'Dependency',
        'parent_question_name' => 'Parent question name',
        'value_dependency_name' => 'value dependency',
        'project_name' => 'Project name',
        'firebase_file' => 'Firebase file (private key)',
        'calories' => 'Calories',
        'calory' =>'Calories',
        'unit_measurement' => 'Unit measurement',
        'options_heading' => 'Nutritional information',
        'options_description:unit' => 'The nutritional information entered for me is each 1 :unit',
        'app_logo' => 'App logo',
        'app_name' => 'App name',
        'app_email' => 'App email',
        'app_phone' => 'App phone',
        'app_mobile' => 'Mobile',
        'logo_dark' => 'Logo dark',
        'logo_light' => 'Logo light',
        'review'=>'Review',
        'add_notes' => 'Add notes',
        'renew_at' => 'Renewal date',
        'calories_description' => 'Total number of calories for exercise / total calories',
        'excersie_cals' => 'Total number of calories for exercise',
        'program_cals' => 'Total number of calories for the programme',
        'total_cals' => 'Total calories',
        'divide_calories' => 'Divide calories',
        'percentage' => 'Percentage',
        'features' => 'Features',
        'sort_order' => 'Sort Order',
    ],
    'tooltip' => [
        'order_limit_per_item' => 'After exceeding the limit, the customer will not pay until approved by one of the branch managers',
        'diameter' => 'The maximum delivery distance is between the customer’s location and the branch from which the order is requested',
    ],
    'suffixes' => [
        'sar' => 'SAR',
        'kcal' => 'Kcal',
        'km' => 'KM',
        'minutes' => 'Minutes',
        'day' => 'Day',
        'month' => 'Month',
        'year' => 'Year',
    ],
    'actions' => [
        'add' => 'Add',
        'send_notifications' => 'Send notifications',
        'send' => 'Send',
        'upload_img' => 'Upload image',
        'loading' => 'Loading',
    ],
    'options' => [
        'all' => 'All',
        'specific' => 'Specific',
    ],

];
