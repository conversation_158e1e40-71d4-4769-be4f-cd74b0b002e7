<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Models\Subscription\Subscription;
use Tasawk\Traits\Publishable;

class Plan extends Model implements HasMedia
{
    use HasFactory, HasTranslations, InteractsWithMedia, Publishable, SoftDeletes;

    public array $translatable = ['title', 'name', 'description', 'notes'];

    protected $fillable = [
        'title',
        'name',
        'description',
        'notes',
        'status',
    ];

    protected $casts = [
        'title' => 'array',
        'name' => 'array',
        'description' => 'array',
        'notes' => 'array',
        'status' => 'boolean',
    ];

    // protected $appends =[
    //     'notes_data',
    //     'sub_images_text',
    // ];

    public function subscripions()
    {
        return $this->hasMany(Subscription::class, 'plan_id');
    }

    public function isSubscribed($user, $plan_price_id)
    {
        return $this->subscripions()->where('customer_id', ($user) ? $user?->id : Auth::id())
            ->where('plan_price_id', $plan_price_id)
            ->exists();
    }

    public function getDefaultAttribute()
    {
        return $this->getMedia('default');
    }

    public function getSubImagesAttribute()
    {
        return $this->getMedia('sub_images');
    }

    public function getStatusTextAttribute()
    {
        return $this->status == 1 ? __('forms.fields.active') : __('forms.fields.inactive');
    }

    public function getNotesTextAttribute()
    {
        $notes = [];
        foreach ($this->notes as $note) {
            if (isset($note['note'])) {
                $notes[] = $note['note'];
            }
        }

        return implode(', ', $notes);
    }

    public function plansPricing(): HasMany
    {
        return $this->hasMany(PlansPricing::class, 'plan_id');
    }



    public function getSubImagesTextAttribute()
    {
        $images = [];
        foreach ($this->getMedia('sub_images') as $image) {

            $images[] = $image->getUrl();
        }

        return $images;
    }

    public function getNotesDataAttribute()
    {
        $notes = [];
        foreach ($this->notes as $note) {
            if (isset($note['note'])) {
                $notes[] = $note['note'];
            }
        }

        return $notes;
    }
}
