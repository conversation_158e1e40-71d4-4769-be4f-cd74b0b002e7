<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Tasawk\Models\Plan;
use Tasawk\Models\PlanFeature;
use Tasawk\Models\PlansPricing;

class PlanPricingFeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing features
        PlanFeature::query()->delete();

        // Features data for different pricing tiers
        $featuresData = [
            1 => [ // 1 month features
                [
                    'name' => [
                        'ar' => 'عدد الوجبات اليومية',
                        'en' => 'Daily Meals Count'
                    ],
                    'description' => [
                        'ar' => '3 وجبات يومياً',
                        'en' => '3 meals daily'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'المتابعة',
                        'en' => 'Follow-up'
                    ],
                    'description' => [
                        'ar' => 'متابعة أسبوعية',
                        'en' => 'Weekly follow-up'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'التمارين',
                        'en' => 'Exercises'
                    ],
                    'description' => [
                        'ar' => 'برنامج تمارين أساسي',
                        'en' => 'Basic exercise program'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'الاستشارات',
                        'en' => 'Consultations'
                    ],
                    'description' => [
                        'ar' => 'استشارة واحدة',
                        'en' => 'One consultation'
                    ]
                ]
            ],
            3 => [ // 3 months features
                [
                    'name' => [
                        'ar' => 'عدد الوجبات اليومية',
                        'en' => 'Daily Meals Count'
                    ],
                    'description' => [
                        'ar' => '4 وجبات يومياً',
                        'en' => '4 meals daily'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'المتابعة',
                        'en' => 'Follow-up'
                    ],
                    'description' => [
                        'ar' => 'متابعة يومية',
                        'en' => 'Daily follow-up'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'التمارين',
                        'en' => 'Exercises'
                    ],
                    'description' => [
                        'ar' => 'برنامج تمارين متقدم',
                        'en' => 'Advanced exercise program'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'الاستشارات',
                        'en' => 'Consultations'
                    ],
                    'description' => [
                        'ar' => '3 استشارات',
                        'en' => '3 consultations'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'دلائل غذائية',
                        'en' => 'Nutritional Guidelines'
                    ],
                    'description' => [
                        'ar' => 'دليل غذائي شامل',
                        'en' => 'Comprehensive nutritional guide'
                    ]
                ]
            ],
            6 => [ // 6 months features
                [
                    'name' => [
                        'ar' => 'عدد الوجبات اليومية',
                        'en' => 'Daily Meals Count'
                    ],
                    'description' => [
                        'ar' => '5 وجبات يومياً',
                        'en' => '5 meals daily'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'المتابعة',
                        'en' => 'Follow-up'
                    ],
                    'description' => [
                        'ar' => 'متابعة مستمرة 24/7',
                        'en' => 'Continuous 24/7 follow-up'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'التمارين',
                        'en' => 'Exercises'
                    ],
                    'description' => [
                        'ar' => 'برنامج تمارين شخصي',
                        'en' => 'Personal exercise program'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'الاستشارات',
                        'en' => 'Consultations'
                    ],
                    'description' => [
                        'ar' => 'استشارات غير محدودة',
                        'en' => 'Unlimited consultations'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'دلائل غذائية',
                        'en' => 'Nutritional Guidelines'
                    ],
                    'description' => [
                        'ar' => 'دليل غذائي متقدم + وصفات',
                        'en' => 'Advanced nutritional guide + recipes'
                    ]
                ],
                [
                    'name' => [
                        'ar' => 'تحليل الجسم',
                        'en' => 'Body Analysis'
                    ],
                    'description' => [
                        'ar' => 'تحليل شامل للجسم شهرياً',
                        'en' => 'Comprehensive monthly body analysis'
                    ]
                ]
            ]
        ];

        $plans = Plan::with('plansPricing')->get();

        foreach ($plans as $plan) {
            foreach ($plan->plansPricing as $pricing) {
                $duration = $pricing->duration;

                // Map duration to months (assuming duration is in days)
                $monthsMapping = [
                    30 => 1,   // 30 days = 1 month
                    90 => 3,   // 90 days = 3 months
                    180 => 6,  // 180 days = 6 months
                    365 => 6,  // 365 days = 6 months (use 6 month features)
                    368 => 6,  // 368 days = 6 months (use 6 month features)
                ];

                $monthsDuration = $monthsMapping[$duration] ?? 1;
                $features = $featuresData[$monthsDuration] ?? $featuresData[1]; // Default to 1 month features

                foreach ($features as $index => $featureData) {
                    PlanFeature::create([
                        'plan_price_id' => $pricing->id,
                        'plan_id' => $plan->id, // Keep for backward compatibility
                        'name' => $featureData['name'],
                        'description' => $featureData['description'],
                        'sort_order' => $index + 1,
                        'status' => true
                    ]);
                }

                $this->command->info("Created features for plan: {$plan->getTranslation('name', 'ar')} - {$duration} days ({$monthsDuration} months)");
            }
        }

        $this->command->info("Successfully created pricing-specific features for all plans.");
    }
}
